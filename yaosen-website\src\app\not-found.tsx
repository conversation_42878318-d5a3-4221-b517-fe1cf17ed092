'use client';

import Link from 'next/link';
import { Home, ArrowLeft } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center px-4">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-blue-600 mb-4">404</h1>
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
            页面未找到
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-md mx-auto">
            抱歉，您访问的页面不存在或已被移动。
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
          >
            <Home className="mr-2" size={20} />
            返回首页
          </Link>
          <button
            onClick={() => window.history.back()}
            className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center"
          >
            <ArrowLeft className="mr-2" size={20} />
            返回上页
          </button>
        </div>
        
        <div className="mt-12">
          <p className="text-gray-500 mb-4">您可能在寻找：</p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/about" className="text-blue-600 hover:text-blue-700 underline">
              关于我们
            </Link>
            <Link href="/services" className="text-blue-600 hover:text-blue-700 underline">
              服务项目
            </Link>
            <Link href="/cases" className="text-blue-600 hover:text-blue-700 underline">
              成功案例
            </Link>
            <Link href="/contact" className="text-blue-600 hover:text-blue-700 underline">
              联系我们
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
