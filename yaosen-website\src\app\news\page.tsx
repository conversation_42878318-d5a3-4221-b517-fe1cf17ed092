import Link from 'next/link';
import { Calendar, User, ArrowRight, Tag } from 'lucide-react';

export default function News() {
  const news = [
    {
      id: 1,
      title: "企业数字化转型的关键要素与实施策略",
      excerpt: "在数字化时代，企业如何抓住机遇，实现成功转型？本文深入分析数字化转型的关键要素和实施策略。",
      date: "2024-01-15",
      author: "张咨询师",
      category: "行业洞察",
      image: "/api/placeholder/400/250",
      featured: true
    },
    {
      id: 2,
      title: "2024年人力资源管理趋势预测",
      excerpt: "随着工作方式的变化和新技术的应用，人力资源管理面临新的挑战和机遇。",
      date: "2024-01-10",
      author: "李经理",
      category: "人力资源",
      image: "/api/placeholder/400/250"
    },
    {
      id: 3,
      title: "中小企业财务管理常见问题及解决方案",
      excerpt: "中小企业在财务管理中经常遇到哪些问题？如何建立有效的财务管理体系？",
      date: "2024-01-05",
      author: "王顾问",
      category: "财务管理",
      image: "/api/placeholder/400/250"
    },
    {
      id: 4,
      title: "企业文化建设的重要性与实践方法",
      excerpt: "优秀的企业文化是企业持续发展的重要基础，如何建设和传承企业文化？",
      date: "2023-12-28",
      author: "陈总监",
      category: "企业文化",
      image: "/api/placeholder/400/250"
    },
    {
      id: 5,
      title: "绩效管理体系设计与实施要点",
      excerpt: "有效的绩效管理体系能够激发员工潜能，提升组织效能。",
      date: "2023-12-20",
      author: "刘专家",
      category: "绩效管理",
      image: "/api/placeholder/400/250"
    },
    {
      id: 6,
      title: "战略规划制定的方法论与实践案例",
      excerpt: "如何制定科学有效的企业战略规划？本文分享实用的方法论和成功案例。",
      date: "2023-12-15",
      author: "张咨询师",
      category: "战略规划",
      image: "/api/placeholder/400/250"
    }
  ];

  const categories = [
    "全部",
    "行业洞察",
    "人力资源",
    "财务管理",
    "企业文化",
    "绩效管理",
    "战略规划"
  ];

  const featuredNews = news.find(item => item.featured);
  const regularNews = news.filter(item => !item.featured);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">新闻资讯</h1>
            <p className="text-xl lg:text-2xl text-blue-100">
              关注行业动态，分享管理智慧，助力企业成长
            </p>
          </div>
        </div>
      </section>

      {/* 主要内容 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          {/* 分类筛选 */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-6 py-2 rounded-full transition-colors duration-200 ${
                  index === 0
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>

          {/* 特色文章 */}
          {featuredNews && (
            <div className="mb-16">
              <h2 className="text-2xl font-bold text-gray-800 mb-8">特色文章</h2>
              <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="grid grid-cols-1 lg:grid-cols-2">
                  <div className="h-64 lg:h-auto bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-500">文章配图</span>
                  </div>
                  <div className="p-8">
                    <div className="flex items-center space-x-4 mb-4">
                      <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                        特色
                      </span>
                      <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                        {featuredNews.category}
                      </span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mb-4">
                      {featuredNews.title}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {featuredNews.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar size={16} />
                          <span>{featuredNews.date}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User size={16} />
                          <span>{featuredNews.author}</span>
                        </div>
                      </div>
                      <Link
                        href={`/news/${featuredNews.id}`}
                        className="text-blue-600 hover:text-blue-700 font-semibold flex items-center"
                      >
                        阅读全文
                        <ArrowRight className="ml-1" size={16} />
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 文章列表 */}
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-8">最新文章</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {regularNews.map((article) => (
                <article key={article.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-200">
                  <div className="h-48 bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-500">文章配图</span>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <Tag size={14} className="text-gray-400" />
                      <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">
                        {article.category}
                      </span>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-3 line-clamp-2">
                      {article.title}
                    </h3>
                    <p className="text-gray-600 mb-4 text-sm leading-relaxed line-clamp-3">
                      {article.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 text-xs text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Calendar size={12} />
                          <span>{article.date}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User size={12} />
                          <span>{article.author}</span>
                        </div>
                      </div>
                      <Link
                        href={`/news/${article.id}`}
                        className="text-blue-600 hover:text-blue-700 text-sm font-semibold flex items-center"
                      >
                        阅读
                        <ArrowRight className="ml-1" size={12} />
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>

          {/* 分页 */}
          <div className="flex justify-center mt-12">
            <div className="flex space-x-2">
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                上一页
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">
                1
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                2
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                3
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50">
                下一页
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* 订阅区域 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            订阅我们的资讯
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            获取最新的行业动态和管理智慧，助力您的企业发展
          </p>
          <div className="max-w-md mx-auto flex">
            <input
              type="email"
              placeholder="请输入您的邮箱地址"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-r-lg font-semibold transition-colors duration-200">
              订阅
            </button>
          </div>
        </div>
      </section>
    </div>
  );
}
