import { 
  Briefcase, 
  Users, 
  Calculator, 
  GraduationCap, 
  Target, 
  BarChart3,
  CheckCircle,
  ArrowRight 
} from 'lucide-react';
import Link from 'next/link';

export default function Services() {
  const services = [
    {
      icon: <Briefcase className="text-blue-600" size={48} />,
      title: "企业管理咨询",
      description: "为企业提供战略规划、组织架构优化、流程改进等全方位的管理咨询服务",
      features: [
        "战略规划与实施",
        "组织架构设计",
        "业务流程优化",
        "管理制度建设",
        "企业文化建设"
      ]
    },
    {
      icon: <Users className="text-green-600" size={48} />,
      title: "人力资源管理",
      description: "提供招聘、培训、绩效管理、薪酬设计等全面的人力资源管理解决方案",
      features: [
        "人才招聘与选拔",
        "员工培训与发展",
        "绩效管理体系",
        "薪酬福利设计",
        "员工关系管理"
      ]
    },
    {
      icon: <Calculator className="text-purple-600" size={48} />,
      title: "财务管理服务",
      description: "协助企业建立完善的财务管理体系，提供财务分析、预算管理等专业服务",
      features: [
        "财务制度建设",
        "预算管理体系",
        "成本控制优化",
        "财务风险管控",
        "投资决策支持"
      ]
    },
    {
      icon: <GraduationCap className="text-orange-600" size={48} />,
      title: "企业培训",
      description: "针对不同层级员工提供定制化的培训课程，提升团队整体素质和能力",
      features: [
        "管理技能培训",
        "专业技能提升",
        "团队建设培训",
        "领导力发展",
        "企业文化培训"
      ]
    },
    {
      icon: <Target className="text-red-600" size={48} />,
      title: "战略规划",
      description: "帮助企业制定清晰的发展战略，明确目标方向，制定实施路径",
      features: [
        "市场分析研究",
        "竞争策略制定",
        "发展目标设定",
        "实施计划制定",
        "战略执行监控"
      ]
    },
    {
      icon: <BarChart3 className="text-indigo-600" size={48} />,
      title: "运营优化",
      description: "通过数据分析和流程优化，提升企业运营效率和管理水平",
      features: [
        "运营流程分析",
        "效率提升方案",
        "质量管理体系",
        "数字化转型",
        "持续改进机制"
      ]
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">服务项目</h1>
            <p className="text-xl lg:text-2xl text-blue-100">
              提供全方位的企业管理服务，助力企业实现可持续发展
            </p>
          </div>
        </div>
      </section>

      {/* 服务概览 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              专业服务体系
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我们提供涵盖企业管理各个方面的专业服务，帮助企业解决管理难题，提升竞争力
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200">
                <div className="mb-6">
                  {service.icon}
                </div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center text-gray-600">
                      <CheckCircle className="text-green-500 mr-2" size={16} />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link
                  href="/contact"
                  className="text-blue-600 hover:text-blue-700 font-semibold flex items-center"
                >
                  了解详情
                  <ArrowRight className="ml-2" size={16} />
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">
              服务流程
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              标准化的服务流程，确保每个项目都能得到专业、高效的执行
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">需求分析</h3>
              <p className="text-gray-600">深入了解企业现状和需求，制定初步方案</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">方案设计</h3>
              <p className="text-gray-600">根据需求分析结果，设计定制化解决方案</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                3
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">实施执行</h3>
              <p className="text-gray-600">专业团队负责方案实施，确保执行效果</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                4
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">跟踪优化</h3>
              <p className="text-gray-600">持续跟踪效果，优化方案，确保长期成功</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-6">
            需要专业的企业管理服务？
          </h2>
          <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
            联系我们的专业团队，获取免费的咨询服务和定制化解决方案
          </p>
          <Link
            href="/contact"
            className="bg-white text-blue-900 hover:bg-gray-100 px-8 py-4 rounded-lg font-semibold text-lg transition-colors duration-200 inline-flex items-center"
          >
            立即咨询
            <ArrowRight className="ml-2" size={20} />
          </Link>
        </div>
      </section>
    </div>
  );
}
