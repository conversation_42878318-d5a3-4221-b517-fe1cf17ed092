import { Users, Target, Award, TrendingUp, Eye, Heart } from 'lucide-react';

export default function About() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">关于耀森</h1>
            <p className="text-xl lg:text-2xl text-blue-100">
              专业的企业管理服务提供商，致力于助力企业实现可持续发展
            </p>
          </div>
        </div>
      </section>

      {/* 公司简介 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-6">公司简介</h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                漯河耀森企业管理有限公司成立于2020年，是一家专业从事企业管理咨询、培训和服务的综合性公司。
                我们拥有一支经验丰富、专业素质过硬的管理咨询团队，致力于为各类企业提供全方位的管理解决方案。
              </p>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-semibold text-gray-800 mb-6">我们的优势</h3>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4"></div>
                    <span className="text-gray-600">拥有多年企业管理咨询经验的专业团队</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4"></div>
                    <span className="text-gray-600">深入了解各行业特点和发展趋势</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4"></div>
                    <span className="text-gray-600">提供定制化的管理解决方案</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4"></div>
                    <span className="text-gray-600">完善的售后服务和持续支持体系</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-gray-50 p-8 rounded-lg">
                <h3 className="text-2xl font-semibold text-gray-800 mb-6">发展历程</h3>
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-600 pl-4">
                    <h4 className="font-semibold text-gray-800">2020年</h4>
                    <p className="text-gray-600">公司成立，开始提供企业管理咨询服务</p>
                  </div>
                  <div className="border-l-4 border-blue-600 pl-4">
                    <h4 className="font-semibold text-gray-800">2021年</h4>
                    <p className="text-gray-600">业务范围扩展，增加人力资源管理服务</p>
                  </div>
                  <div className="border-l-4 border-blue-600 pl-4">
                    <h4 className="font-semibold text-gray-800">2022年</h4>
                    <p className="text-gray-600">建立完善的培训体系，推出企业培训服务</p>
                  </div>
                  <div className="border-l-4 border-blue-600 pl-4">
                    <h4 className="font-semibold text-gray-800">2023年</h4>
                    <p className="text-gray-600">服务客户超过100家，获得行业认可</p>
                  </div>
                  <div className="border-l-4 border-blue-600 pl-4">
                    <h4 className="font-semibold text-gray-800">2024年</h4>
                    <p className="text-gray-600">持续创新，提供更加专业的管理服务</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 企业文化 */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">企业文化</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我们的企业文化体现了我们的价值观和使命，指导着我们为客户提供优质服务
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-8 bg-white rounded-lg shadow-lg">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Eye className="text-blue-600" size={32} />
              </div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">企业愿景</h3>
              <p className="text-gray-600 leading-relaxed">
                成为河南省领先的企业管理服务提供商，为企业发展贡献专业力量，推动区域经济繁荣发展。
              </p>
            </div>
            
            <div className="text-center p-8 bg-white rounded-lg shadow-lg">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="text-green-600" size={32} />
              </div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">企业使命</h3>
              <p className="text-gray-600 leading-relaxed">
                通过专业的管理咨询和培训服务，帮助企业提升管理水平，实现可持续发展，创造更大价值。
              </p>
            </div>
            
            <div className="text-center p-8 bg-white rounded-lg shadow-lg">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Heart className="text-purple-600" size={32} />
              </div>
              <h3 className="text-2xl font-semibold text-gray-800 mb-4">核心价值观</h3>
              <p className="text-gray-600 leading-relaxed">
                专业、诚信、创新、共赢。我们坚持以客户为中心，提供专业服务，与客户共同成长。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 团队介绍 */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4">专业团队</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              我们拥有一支经验丰富、专业素质过硬的管理咨询团队
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">张总经理</h3>
              <p className="text-gray-600 mb-2">总经理</p>
              <p className="text-sm text-gray-500">15年企业管理经验</p>
            </div>
            
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">李咨询师</h3>
              <p className="text-gray-600 mb-2">高级咨询师</p>
              <p className="text-sm text-gray-500">战略规划专家</p>
            </div>
            
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">王经理</h3>
              <p className="text-gray-600 mb-2">人力资源经理</p>
              <p className="text-sm text-gray-500">人力资源管理专家</p>
            </div>
            
            <div className="text-center">
              <div className="w-32 h-32 bg-gray-300 rounded-full mx-auto mb-4"></div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">陈顾问</h3>
              <p className="text-gray-600 mb-2">财务顾问</p>
              <p className="text-sm text-gray-500">财务管理专家</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
