exports.id=413,exports.ids=[413],exports.modules={373:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,8928))},440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(1658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},686:(e,s,t)=>{Promise.resolve().then(t.bind(t,4413))},1135:()=>{},2199:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},3734:(e,s,t)=>{Promise.resolve().then(t.bind(t,7347))},4293:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.bind(t,6246))},4413:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode\\\\www1\\\\yaosen-website\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode\\www1\\yaosen-website\\src\\app\\not-found.tsx","default")},6246:(e,s,t)=>{"use strict";t.d(s,{default:()=>x});var r=t(687),l=t(3210),i=t(5814),a=t.n(i),n=t(8340),c=t(3931),d=t(1860),o=t(2941);let x=()=>{let[e,s]=(0,l.useState)(!1),t=[{href:"/",label:"首页"},{href:"/about",label:"关于我们"},{href:"/services",label:"服务项目"},{href:"/cases",label:"成功案例"},{href:"/news",label:"新闻资讯"},{href:"/contact",label:"联系我们"}];return(0,r.jsxs)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:[(0,r.jsx)("div",{className:"bg-blue-900 text-white py-2",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 flex justify-between items-center text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.A,{size:14}),(0,r.jsx)("span",{children:"400-123-4567"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{size:14}),(0,r.jsx)("span",{children:"<EMAIL>"})]})]}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("span",{children:"专业企业管理服务 \xb7 值得信赖的合作伙伴"})})]})}),(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,r.jsxs)(a(),{href:"/",className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-xl",children:"耀"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-800",children:"漯河耀森"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"企业管理有限公司"})]})]}),(0,r.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:t.map(e=>(0,r.jsxs)(a(),{href:e.href,className:"text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative group",children:[e.label,(0,r.jsx)("span",{className:"absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-200 group-hover:w-full"})]},e.href))}),(0,r.jsx)("button",{className:"md:hidden p-2 rounded-lg hover:bg-gray-100",onClick:()=>s(!e),children:e?(0,r.jsx)(d.A,{size:24}):(0,r.jsx)(o.A,{size:24})})]}),e&&(0,r.jsx)("div",{className:"md:hidden py-4 border-t border-gray-200",children:t.map(e=>(0,r.jsx)(a(),{href:e.href,className:"block py-3 px-4 text-gray-700 hover:text-blue-600 hover:bg-gray-50 transition-colors duration-200",onClick:()=>s(!1),children:e.label},e.href))})]})]})}},7347:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(687),l=t(5814),i=t.n(l),a=t(2192),n=t(8559);function c(){return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center px-4",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-9xl font-bold text-blue-600 mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-3xl lg:text-4xl font-bold text-gray-800 mb-4",children:"页面未找到"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-md mx-auto",children:"抱歉，您访问的页面不存在或已被移动。"})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(i(),{href:"/",className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center",children:[(0,r.jsx)(a.A,{className:"mr-2",size:20}),"返回首页"]}),(0,r.jsxs)("button",{onClick:()=>window.history.back(),className:"border border-gray-300 text-gray-700 hover:bg-gray-50 px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center justify-center",children:[(0,r.jsx)(n.A,{className:"mr-2",size:20}),"返回上页"]})]}),(0,r.jsxs)("div",{className:"mt-12",children:[(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"您可能在寻找："}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[(0,r.jsx)(i(),{href:"/about",className:"text-blue-600 hover:text-blue-700 underline",children:"关于我们"}),(0,r.jsx)(i(),{href:"/services",className:"text-blue-600 hover:text-blue-700 underline",children:"服务项目"}),(0,r.jsx)(i(),{href:"/cases",className:"text-blue-600 hover:text-blue-700 underline",children:"成功案例"}),(0,r.jsx)(i(),{href:"/contact",className:"text-blue-600 hover:text-blue-700 underline",children:"联系我们"})]})]})]})})}},8042:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v,metadata:()=>f});var r=t(7413),l=t(2376),i=t.n(l),a=t(8726),n=t.n(a);t(1135);var c=t(8928),d=t(4536),o=t.n(d),x=t(9046),h=t(1750),m=t(343),j=t(3148);let b=()=>(0,r.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:"耀"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-bold",children:"漯河耀森"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"企业管理有限公司"})]})]}),(0,r.jsx)("p",{className:"text-gray-400 text-sm leading-relaxed",children:"专业的企业管理服务提供商，致力于为企业提供全方位的管理咨询、培训和解决方案，助力企业持续发展。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/about",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"关于我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/services",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"服务项目"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/cases",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"成功案例"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/news",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"新闻资讯"})}),(0,r.jsx)("li",{children:(0,r.jsx)(o(),{href:"/contact",className:"text-gray-400 hover:text-white transition-colors duration-200",children:"联系我们"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"核心服务"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsx)("li",{className:"text-gray-400",children:"企业管理咨询"}),(0,r.jsx)("li",{className:"text-gray-400",children:"人力资源管理"}),(0,r.jsx)("li",{className:"text-gray-400",children:"财务管理服务"}),(0,r.jsx)("li",{className:"text-gray-400",children:"企业培训"}),(0,r.jsx)("li",{className:"text-gray-400",children:"战略规划"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"联系我们"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{size:18,className:"text-blue-400 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"河南省漯河市"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"经济技术开发区"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.A,{size:18,className:"text-blue-400"}),(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:"400-123-4567"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.A,{size:18,className:"text-blue-400"}),(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(j.A,{size:18,className:"text-blue-400 mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"周一至周五"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"9:00 - 18:00"})]})]})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-800 mt-12 pt-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2024 漯河耀森企业管理有限公司. 保留所有权利."}),(0,r.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,r.jsx)(o(),{href:"/privacy",className:"text-gray-400 hover:text-white text-sm transition-colors duration-200",children:"隐私政策"}),(0,r.jsx)(o(),{href:"/terms",className:"text-gray-400 hover:text-white text-sm transition-colors duration-200",children:"服务条款"})]})]})})]})}),f={title:"漯河耀森企业管理有限公司 - 专业企业管理服务",description:"漯河耀森企业管理有限公司是一家专业的企业管理服务公司，提供企业咨询、管理培训、人力资源、财务管理等全方位服务。"};function v({children:e}){return(0,r.jsx)("html",{lang:"zh-CN",children:(0,r.jsxs)("body",{className:`${i().variable} ${n().variable} antialiased`,children:[(0,r.jsx)(c.default,{}),(0,r.jsx)("main",{children:e}),(0,r.jsx)(b,{})]})})}},8928:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\vscode\\\\www1\\\\yaosen-website\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\vscode\\www1\\yaosen-website\\src\\components\\Navigation.tsx","default")},9071:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))}};